import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: Request) {
  try {
    const { code, userId } = await request.json()

    if (!code) {
      return NextResponse.json(
        { error: 'Invitation code is required' },
        { status: 400 }
      )
    }

    // 标记邀请码为已使用
    const { data, error } = await supabase
      .from('invitation_codes')
      .update({ 
        is_used: true, 
        used_at: new Date().toISOString(),
        used_by: userId || null 
      })
      .eq('code', code.trim().toUpperCase())
      .eq('is_used', false) // 确保只能使用一次
      .select()

    if (error || !data || data.length === 0) {
      // 统一错误信息，不泄露具体原因
      return NextResponse.json(
        { error: 'Failed to process invitation code' },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      success: true,
      message: 'Invitation code marked as used' 
    })

  } catch (error) {
    console.error('Error using invitation code:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

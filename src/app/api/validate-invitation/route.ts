import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: Request) {
  try {
    const { code } = await request.json()

    if (!code || typeof code !== 'string') {
      return NextResponse.json(
        { error: 'Invitation code is required' },
        { status: 400 }
      )
    }

    const searchCode = code.trim().toUpperCase()

    // 使用普通客户端查询（依赖 RLS 策略）
    const { data, error } = await supabase
      .from('invitation_codes')
      .select('*')
      .eq('code', searchCode)
      .eq('is_used', false)
      .single()

    // 统一错误信息，不泄露具体原因
    if (error || !data) {
      return NextResponse.json(
        { valid: false, error: 'Verification failed' },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      valid: true, 
      invitationId: data.id 
    })

  } catch (error) {
    console.error('Error validating invitation code:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

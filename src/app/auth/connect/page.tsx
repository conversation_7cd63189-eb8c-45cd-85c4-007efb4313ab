'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { AuthLoading } from '@/components/ui/loading'
import type { User } from '@supabase/supabase-js'

export default function ConnectPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [user, setUser] = useState<User | null>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check if user is authenticated and if LinkedIn is already connected
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }
      
      // Check if LinkedIn is already connected
      const linkedinIdentity = user.identities?.find(
        identity => identity.provider === 'linkedin_oidc'
      )
      
      if (linkedinIdentity) {
        // LinkedIn already connected, go to conversation
        localStorage.removeItem('phone_number')
        router.push('/conversation')
        return
      }
      
      setUser(user)
      setIsCheckingAuth(false)
    }
    checkUser()

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.identities)
        
        if (event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') {
          // Check if LinkedIn identity is linked
          const linkedinIdentity = session?.user?.identities?.find(
            identity => identity.provider === 'linkedin_oidc'
          )
          if (linkedinIdentity) {
            // LinkedIn connected successfully
            localStorage.removeItem('phone_number')
            router.push('/conversation')
          }
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [router])

  const handleLinkedInConnect = async () => {
    setLoading(true)
    setError('')

    try {
      const { error } = await supabase.auth.linkIdentity({
        provider: 'linkedin_oidc',
        options: {
          redirectTo: `${window.location.origin}/auth/connect`
        }
      })

      if (error) {
        setError(error.message)
        setLoading(false)
      }
      // If no error, user will be redirected to LinkedIn
    } catch (err) {
      setError('Something went wrong')
      setLoading(false)
    }
  }


  if (isCheckingAuth) {
    return <AuthLoading />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Main Card */}
      <div className="relative w-full max-w-md">
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-4 shadow-lg">
            <img 
              src="/emily.svg" 
              alt="Emily AI" 
              className="w-8 h-8 text-white"
            />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Connect LinkedIn</h1>
          <p className="text-gray-600">Link your LinkedIn profile to complete setup</p>
        </div>

        {/* Form Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8">
          {/* LinkedIn Icon and Info */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-[#0077B5] rounded-xl mb-4 shadow-md">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </div>
            <p className="text-gray-600 text-sm">
              We&apos;ll use your LinkedIn profile to personalize your Emily AI experience
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Connect Button */}
          <Button
            onClick={handleLinkedInConnect}
            disabled={loading}
            className="w-full h-12 bg-[#0077B5] hover:bg-[#005885] text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed mb-4"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Connecting...
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
                Connect LinkedIn
              </div>
            )}
          </Button>


          {/* Info Text */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              Your LinkedIn data is secure and will only be used to enhance your AI conversation experience.
            </p>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="text-center mt-6 space-y-4">
          <div className="pt-2 border-t border-gray-200">
            <button 
              onClick={() => router.push('/auth/verify')}
              className="text-gray-500 hover:text-gray-700 text-sm transition-colors cursor-pointer"
            >
              ← Back to Verification
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

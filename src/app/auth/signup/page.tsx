'use client'

import { useState } from 'react'
import * as React from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { PhoneInput } from '@/components/ui/phone-input'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { AuthLoading } from '@/components/ui/loading'

export default function SignupPage() {
  const [phone, setPhone] = useState<string>('')
  const [invitationCode, setInvitationCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const router = useRouter()

  // Check authentication status on component mount
  React.useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        // User is already authenticated, redirect based on LinkedIn connection
        const linkedinIdentity = user.identities?.find(
          identity => identity.provider === 'linkedin_oidc'
        )
        
        if (linkedinIdentity) {
          // Both phone and LinkedIn verified, go to conversation page
          router.push('/conversation')
          return
        } else {
          // Phone verified but no LinkedIn, go to connect page
          router.push('/auth/connect')
          return
        }
      }
      
      // User is not authenticated, show signup form
      setIsCheckingAuth(false)
    }
    
    checkAuth()
  }, [router])

  const validatePhoneNumber = (phone: string) => {
    // US phone number validation
    if (!phone || phone.trim() === '') return false
    
    // Must include +1 prefix
    if (!phone.includes('+1')) return false
    
    // Extract digits only
    const digits = phone.replace(/\D/g, '')
    
    // Should have exactly 11 digits (1 + 10 digit US number)
    // Or exactly 10 digits (without country code, we'll add +1)
    return digits.length === 11 || digits.length === 10
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validatePhoneNumber(phone)) {
      setError('Please enter a valid US phone number')
      setLoading(false)
      return
    }

    if (!invitationCode.trim()) {
      setError('Please enter an invitation code')
      setLoading(false)
      return
    }

    try {
      // 1. 先验证邀请码
      const inviteResponse = await fetch('/api/validate-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: invitationCode.trim() }),
      })

      const inviteData = await inviteResponse.json()

      if (!inviteResponse.ok || !inviteData.valid) {
        setError(inviteData.error || 'Invalid invitation code')
        setLoading(false)
        return
      }

      // 2. 邀请码验证通过，发送OTP
      const cleanPhone = phone.replace(/\D/g, '')
      // 如果已经包含国家代码1，直接使用；否则添加+1
      const e164Phone = cleanPhone.length === 11 ? `+${cleanPhone}` : `+1${cleanPhone}`
      
      const { error } = await supabase.auth.signInWithOtp({
        phone: e164Phone,
      })

      if (error) {
        setError(error.message)
      } else {
        // Store phone number and invitation code for verification step
        localStorage.setItem('phone_number', e164Phone)
        localStorage.setItem('invitation_code', invitationCode.trim().toUpperCase())
        router.push('/auth/verify')
      }
    } catch (err) {
      console.error('Signup error:', err)
      setError('Something went wrong')
    } finally {
      setLoading(false)
    }
  }

  if (isCheckingAuth) {
    return <AuthLoading />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Main Card */}
      <div className="relative w-full max-w-md">
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-4 shadow-lg">
            <img 
              src="/emily.svg" 
              alt="Emily AI" 
              className="w-8 h-8 text-white"
            />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Join Emily</h1>
          <p className="text-gray-600">Create your account with an invitation code</p>
        </div>

        {/* Form Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Phone Number Field */}
            <div className="space-y-2">
              <label htmlFor="phone" className="block text-sm font-semibold text-gray-700">
                Phone Number
              </label>
              <PhoneInput
                value={phone}
                onChange={(value) => setPhone(value)}
                className="w-full h-12 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 text-base font-medium focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:border-blue-500 transition-all duration-200 hover:border-gray-300"
              />
            </div>

            {/* Invitation Code Field */}
            <div className="space-y-2">
              <label htmlFor="invitationCode" className="block text-sm font-semibold text-gray-700">
                Invitation Code
              </label>
              <Input
                id="invitationCode"
                type="text"
                value={invitationCode}
                onChange={(e) => setInvitationCode(e.target.value)}
                placeholder="Enter invitation code"
                className="w-full h-12 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 text-base font-medium focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:border-blue-500 transition-all duration-200 hover:border-gray-300"
                required
              />
              <p className="text-xs text-gray-500">
                Don&apos;t have one?{' '}
                <button 
                  type="button"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    const invitationFormUrl = process.env.NEXT_PUBLIC_INVITATION_FORM
                    if (invitationFormUrl) {
                      window.open(invitationFormUrl, '_blank')
                    } else {
                      alert('Contact support to get an invitation code')
                    }
                  }}
                  className="text-blue-600 hover:text-blue-700 cursor-pointer font-medium hover:underline transition-colors"
                >
                  Get it here
                </button>
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={loading}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Creating account...
                </div>
              ) : (
                'Create Account'
              )}
            </Button>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              By continuing, you agree to our{' '}
              <button 
                type="button"
                onClick={(e) => {
                  e.preventDefault()
                  window.open('/terms', '_blank')
                }}
                className="text-blue-600 hover:text-blue-700 cursor-pointer font-medium hover:underline transition-colors"
              >
                Terms of Service
              </button>
              {' '}and{' '}
              <button 
                type="button"
                onClick={(e) => {
                  e.preventDefault()
                  window.open('/privacy', '_blank')
                }}
                className="text-blue-600 hover:text-blue-700 cursor-pointer font-medium hover:underline transition-colors"
              >
                Privacy Policy
              </button>
            </p>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="text-center mt-6 space-y-4">
          <p className="text-gray-600">
            Already have an account?{' '}
            <button 
              onClick={() => router.push('/auth/login')}
              className="text-blue-600 hover:text-blue-700 font-semibold hover:underline transition-colors"
            >
              Sign in
            </button>
          </p>
          
          <div className="pt-2 border-t border-gray-200">
            <button 
              onClick={() => router.push('/')}
              className="text-gray-500 hover:text-gray-700 text-sm transition-colors cursor-pointer"
            >
              ← Back to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { AuthLoading } from '@/components/ui/loading'

export default function VerifyPage() {
  const [token, setToken] = useState('')
  const [phone, setPhone] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const [resendLoading, setResendLoading] = useState(false)
  const [resendCountdown, setResendCountdown] = useState(0)
  const router = useRouter()

  useEffect(() => {
    const checkAuthAndPhone = async () => {
      // First check if user is already fully authenticated
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        // User is already authenticated, check LinkedIn connection
        const linkedinIdentity = user.identities?.find(
          identity => identity.provider === 'linkedin_oidc'
        )
        
        if (linkedinIdentity) {
          // Both phone and LinkedIn verified, go to conversation page
          router.push('/conversation')
          return
        } else {
          // Phone verified but no LinkedIn, go to connect page
          router.push('/auth/connect')
          return
        }
      }
      
      // User is not authenticated, check if we have phone number for verification
      const phoneNumber = localStorage.getItem('phone_number')
      if (!phoneNumber) {
        router.push('/auth/login')
        return
      }
      
      setPhone(phoneNumber)
      setIsCheckingAuth(false)
      // Start initial countdown
      setResendCountdown(60)
    }
    
    checkAuthAndPhone()
  }, [router])

  // Countdown timer effect
  useEffect(() => {
    if (resendCountdown > 0) {
      const timer = setTimeout(() => {
        setResendCountdown(resendCountdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [resendCountdown])

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const { data, error } = await supabase.auth.verifyOtp({
        phone: phone,
        token: token,
        type: 'sms'
      })

      if (error) {
        setError(error.message)
      } else {
        // OTP验证成功，标记邀请码为已使用
        const invitationCode = localStorage.getItem('invitation_code')
        if (invitationCode && data.user) {
          try {
            await fetch('/api/use-invitation', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ 
                code: invitationCode,
                userId: data.user.id 
              }),
            })
          } catch (inviteError) {
            console.error('Failed to mark invitation code as used:', inviteError)
            // 不阻断流程，只记录错误
          }
        }
        
        // Phone verification successful, now connect LinkedIn
        router.push('/auth/connect')
      }
    } catch (err) {
      setError('Something went wrong')
    } finally {
      setLoading(false)
    }
  }

  const handleResendCode = async () => {
    setResendLoading(true)
    setError('')

    try {
      const { error } = await supabase.auth.signInWithOtp({
        phone: phone,
        options: {
          shouldCreateUser: false
        }
      })

      if (error) {
        setError(error.message)
      } else {
        // Reset countdown and show success message
        setResendCountdown(60)
        setError('')
        // You could show a success message here if needed
      }
    } catch (err) {
      setError('Failed to resend code')
    } finally {
      setResendLoading(false)
    }
  }

  if (isCheckingAuth) {
    return <AuthLoading />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Main Card */}
      <div className="relative w-full max-w-md">
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-4 shadow-lg">
            <img 
              src="/emily.svg" 
              alt="Emily AI" 
              className="w-8 h-8 text-white"
            />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Verify Phone</h1>
          <p className="text-gray-600">Enter the verification code sent to</p>
          <p className="text-gray-800 font-semibold">{phone}</p>
        </div>

        {/* Form Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8">
          <form onSubmit={handleVerifyCode} className="space-y-6">
            {/* Verification Code Field */}
            <div className="space-y-2">
              <label htmlFor="token" className="block text-sm font-semibold text-gray-700">
                Verification Code
              </label>
              <Input
                id="token"
                type="text"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                placeholder="123456"
                className="w-full h-12 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 text-base font-medium focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:border-blue-500 transition-all duration-200 hover:border-gray-300 text-center tracking-widest"
                required
                maxLength={6}
                autoComplete="one-time-code"
              />
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={loading}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Verifying...
                </div>
              ) : (
                'Verify Code'
              )}
            </Button>
          </form>

          {/* Resend Section */}
          <div className="mt-6 text-center space-y-3">
            <p className="text-xs text-gray-500">
              Didn&apos;t receive the code? Check your messages.
            </p>
            
            {resendCountdown > 0 ? (
              <p className="text-sm text-gray-600">
                Resend code in <span className="font-semibold text-blue-600">{resendCountdown}s</span>
              </p>
            ) : (
              <Button
                type="button"
                variant="ghost"
                onClick={handleResendCode}
                disabled={resendLoading}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold transition-colors"
              >
                {resendLoading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    Sending...
                  </div>
                ) : (
                  'Resend Code'
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Navigation Links */}
        <div className="text-center mt-6 space-y-4">
          <div className="pt-2 border-t border-gray-200">
            <button 
              onClick={() => router.push('/auth/login')}
              className="text-gray-500 hover:text-gray-700 text-sm transition-colors cursor-pointer"
            >
              ← Back to Phone Login
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { ElevenLabsConversation } from '@/components/ElevenLabsConversation'
import { ProfileLoading } from '@/components/ui/loading'

export default function SuccessPage() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [user, setUser] = useState<any>(null)
  const [linkedinConnected, setLinkedinConnected] = useState(false)
  const [userName, setUserName] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [showVoiceInterface, setShowVoiceInterface] = useState(false)
  const [conversationId, setConversationId] = useState<string>('')
  const [profileComplete, setProfileComplete] = useState<boolean | null>(null)
  const [checkingStatus, setCheckingStatus] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true) // 添加初始加载状态
  const router = useRouter()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      
      // If user is not authenticated, redirect to login
      if (!user) {
        router.push('/auth/login')
        return
      }
      
      if (user) {
        const linkedinIdentity = user.identities?.find(
          identity => identity.provider === 'linkedin_oidc'
        )
        setLinkedinConnected(!!linkedinIdentity)

        // Extract user name from LinkedIn identity data
        let extractedName = ''
        if (linkedinIdentity?.identity_data) {
          const identityData = linkedinIdentity.identity_data
          // Try different name fields from LinkedIn
          extractedName = identityData.given_name || 
                        identityData.name || 
                        identityData.first_name ||
                        identityData.full_name ||
                        ''
          
          console.log('LinkedIn identity data:', identityData)
          console.log('Extracted name:', extractedName)
        }
        
        setUserName(extractedName)
        
        // Check profile completion status when user is loaded
        if (user?.id) {
          await checkProfileCompleteStatus(user.id)
        }
      }
      
      // 完成所有初始化后设置加载完成
      setIsInitialLoading(false)
    }
    
    getUser()

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event)
        
        if (event === 'SIGNED_OUT' || !session?.user) {
          // User signed out, redirect to login
          router.push('/auth/login')
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const checkProfileCompleteStatus = async (userId: string) => {
    if (!userId) return
    
    setCheckingStatus(true)
    try {
      console.log('Checking profile completion status for user:', userId)
      
      // First query: Check if any conversation is complete
      const { data: conversationsData, error: conversationsError } = await supabase
        .from('conversations')
        .select('profile_collection_complete')
        .eq('user_id', userId)

      if (conversationsError) {
        console.error('Error checking profile completion status:', conversationsError)
        return
      }

      console.log('User conversations data:', conversationsData)
      
      // Check if any conversation has profile_collection_complete = true
      const profileComplete = conversationsData?.some(conv => conv.profile_collection_complete === true) || false
      
      if (profileComplete) {
        console.log('Profile complete via conversation completion')
        setProfileComplete(true)
        return
      }

      // Second query: Check total call duration if profile not complete
      console.log('Profile not complete, checking call duration...')
      const { data: callStatsData, error: callStatsError } = await supabase
        .from('user_call_stats')
        .select('total_call_duration_seconds')
        .eq('user_id', userId)
        .maybeSingle() // Use maybeSingle() instead of single() to handle no records gracefully

      if (callStatsError) {
        console.error('Error checking call stats:', callStatsError)
        setProfileComplete(false)
        return
      }

      console.log('User call stats data:', callStatsData)
      
      // If no record exists, callStatsData will be null
      const totalDuration = callStatsData?.total_call_duration_seconds || 0
      const timeLimit = 780 // 13 minutes in seconds
      const timeLimitReached = totalDuration >= timeLimit
      
      console.log(`Total duration: ${totalDuration}s, Time limit: ${timeLimit}s, Limit reached: ${timeLimitReached}`)
      
      setProfileComplete(timeLimitReached)
    } catch (err) {
      console.error('Error checking profile completion status:', err)
    } finally {
      setCheckingStatus(false)
    }
  }

  const handleLogout = async () => {
    setLoading(true)
    
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out:', error)
      } else {
        localStorage.removeItem('phone_number')
        router.push('/auth/login')
      }
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleReadyClick = () => {
    setShowVoiceInterface(true)
  }

  // Handle conversation start
  const handleConversationId = (convId: string) => {
    console.log('Received conversation ID:', convId)
    setConversationId(convId)
  }

  // Handle conversation end - check profile completion
  const handleConversationEnd = () => {
    console.log('Conversation ended, checking profile completion status')
    if (user?.id) {
      checkProfileCompleteStatus(user.id)
    }
  }

  // Show loading screen during initial check
  if (isInitialLoading) {
    return <ProfileLoading />
  }

  // Show completion screen if profile is complete
  if (profileComplete === true) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        </div>

        {/* Main Card */}
        <div className="relative w-full max-w-lg">
          {/* Success Icon */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-6 shadow-lg">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Profile Complete!</h1>
            <p className="text-gray-600">
              Great job {userName}! Your conversation is now complete.
            </p>
          </div>

          {/* Completion Card */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 text-center">
            <div className="mb-6">
              <div className="text-4xl mb-4">🎉</div>
              <p className="text-gray-700 text-lg leading-relaxed">
                Emily has gathered all the information needed to help with your career journey. 
                Your personalized experience is ready!
              </p>
            </div>

            <Button
              onClick={handleLogout}
              disabled={loading}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Signing out...
                </div>
              ) : (
                'Sign Out'
              )}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (showVoiceInterface) {
    return (
      <ElevenLabsConversation
        agentId={process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID || ''}
        userId={user?.id || user?.email || user?.phone || undefined}
        userName={userName || undefined}
        onBack={() => setShowVoiceInterface(false)}
        onSignOut={handleLogout}
        loading={loading}
        onConversationId={handleConversationId}
        onConversationEnd={handleConversationEnd}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Main Card */}
      <div className="relative w-full max-w-lg">
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-4 shadow-lg">
            <img 
              src="/emily.svg" 
              alt="Emily AI" 
              className="w-8 h-8 text-white"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {userName ? `Hello ${userName}!` : 'Hello!'}
          </h1>
          <p className="text-gray-600">Ready to start your conversation with Emily?</p>
        </div>

        {/* Main Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 mb-6">
          {/* Introduction */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-blue-600 mb-6 leading-tight">
              Your Match<br />
              Starts With a Call
            </h2>
            <p className="text-gray-600 leading-relaxed">
              During this call, Emily learns your basic info, listens to
              your story, and understands the kind of relationship
              you&apos;re looking for.
            </p>
          </div>

          {/* Tips Section */}
          <div className="bg-blue-50 border border-blue-100 rounded-2xl p-6 mb-8">
            <h3 className="text-blue-600 font-semibold mb-6 text-lg">
              What to expect from your call
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-4 mt-2"></div>
                <span className="text-blue-800">Just 10 minutes to begin your story.</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-4 mt-2"></div>
                <span className="text-blue-800">The more open you are, the faster Emily matches.</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-4 mt-2"></div>
                <span className="text-blue-800">Join from web or phone, whatever feels natural.</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-4 mt-2"></div>
                <span className="text-blue-800">It&apos;s like talking to a friend, not filling a form.</span>
              </div>
            </div>
          </div>

          {/* Start Button */}
          <Button
            onClick={handleReadyClick}
            className="w-full h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-lg cursor-pointer"
          >
            Let&apos;s go
          </Button>

          {/* User Status */}
          {user && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <span>Phone verified</span>
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                  <span>LinkedIn connected</span>
                </div>
              </div>
            </div>
          )}
        </div>

      </div>
    </div>
  )
}

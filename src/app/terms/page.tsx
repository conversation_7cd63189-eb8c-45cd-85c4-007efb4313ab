'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Footer from '@/components/Footer'

export default function TermsOfService() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between xl:min-w-[950px]">
          {/* Logo */}
          <button 
            onClick={() => router.push('/')}
            className="flex items-center space-x-3 hover:opacity-80 transition-opacity cursor-pointer"
          >
            <img 
              src="/emily.svg" 
              alt="Emily AI Logo" 
              className="w-8 h-8 md:w-11 md:h-11"
            />
            <span className="text-lg md:text-xl font-bold text-[#005EFF]">Emily</span>
          </button>


          {/* Sign In Button - responsive sizing */}
          {/* <button
            onClick={() => router.push('/auth/login')}
            className="flex w-[80px] h-[40px] md:w-[121px] md:h-[51px] p-[8px] md:p-[11px] justify-center items-center gap-[10px] flex-shrink-0 rounded-[8px] md:rounded-[10px] font-semibold text-[#005eff] bg-[#0077FF33] hover:bg-[#99cfff] transition-colors cursor-pointer text-sm md:text-base"
          >
            Sign in
          </button> */}
        </div>
      </header>

      {/* Main Content */}
      <main className="pt-24 pb-16 px-6">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-black mb-2">
              Terms of Service
            </h1>
            <p className="text-gray-600 text-sm">
              Last updated: January 2025
            </p>
          </div>

          {/* Content */}
          <div className="prose prose-lg max-w-none text-black space-y-8">
            <p className="text-lg leading-relaxed">
              These Terms of Service (&quot;Terms&quot;) govern your use of Emily, an AI-powered matchmaking assistant that facilitates introductions through SMS, phone calls, and conversational AI. By sharing your contact information and using Emily, you agree to these Terms.
            </p>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Definitions</h2>
              <div className="space-y-4">
                <p>
                  <strong>&quot;Service&quot;:</strong> The Emily platform and its functionalities, including AI-assisted matchmaking, SMS communication, phone-based interactions, and user data handling.
                </p>
                <p>
                  <strong>&quot;User&quot;:</strong> An individual who provides their phone number, company email, and/or LinkedIn information to Emily.
                </p>
                <p>
                  <strong>&quot;Match&quot;:</strong> A connection facilitated by Emily between two consenting users.
                </p>
                <p>
                  <strong>&quot;Profile Information&quot;:</strong> Information provided by the user, including public LinkedIn data, job title, company affiliation, and personal preferences.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Eligibility</h2>
              <p className="text-lg leading-relaxed">
                You must be at least 18 years old to use this Service. By using Emily, you represent that you meet this requirement. Emily reserves the right to verify user age and deny access for any violations.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Registration and Consent Flow</h2>
              <p className="text-lg leading-relaxed mb-4">Emily&apos;s registration and onboarding includes the following steps:</p>
              <ol className="list-decimal list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>You initiate contact with Emily via a designated phone number, link, or referral.</li>
                <li>You provide your phone number, company email, and optionally your LinkedIn profile.</li>
                <li>By proceeding, you:
                  <ul className="list-disc list-inside ml-6 mt-2 space-y-1">
                    <li>Agree to these Terms of Service.</li>
                    <li>Consent to receive SMS and/or phone calls from Emily.</li>
                    <li>Authorize Emily to use your public LinkedIn data and submitted information to facilitate matches.</li>
                  </ul>
                </li>
              </ol>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Communication and Marketing Consent</h2>
              <p className="text-lg leading-relaxed mb-4">
                By submitting your phone number and company email, you consent to receive communications from Emily, including:
              </p>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>SMS messages about matches, updates, or onboarding.</li>
                <li>Occasional announcements, feature launches, or service tips.</li>
              </ul>
              <p className="text-lg leading-relaxed">
                You may opt out at any time by replying STOP to SMS messages or by contacting us at <a href="mailto:<EMAIL>" className="text-black hover:underline"><EMAIL></a>
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">AI Interaction and Third-Party Processing</h2>
              <p className="text-lg leading-relaxed mb-4">
                Emily uses third-party AI providers such as OpenAI to analyze conversations, generate dialogue, and match users. By using Emily, you agree that:
              </p>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>Your messages and preferences may be processed by external AI services.</li>
                <li>Transcripts of phone conversations may be analyzed to improve match quality.</li>
                <li>Data may be retained by AI providers per their respective terms.</li>
              </ul>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Privacy and Data Use</h2>
              <p className="text-lg leading-relaxed mb-4">
                Emily respects your privacy and handles user data with care. By using Emily, you agree to the following:
              </p>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>Your LinkedIn data (public) and company email may be used to create your matchmaking profile.</li>
                <li>Your data may be used in aggregate and anonymous form to improve the Service.</li>
                <li>Your phone number and email will never be shared with other users without explicit opt-in from both parties.</li>
                <li>Emily may use hashed versions of your contact data for advertising retargeting purposes (e.g., Meta, LinkedIn Ads).</li>
              </ul>
              <p className="text-lg leading-relaxed">
                Please refer to our <a href="/privacy" className="text-black hover:underline">Privacy Policy</a> for full details.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Service Access and Limitations</h2>
              <p className="text-lg leading-relaxed">
                Use of Emily is currently free of charge. If paid plans are introduced in the future, you will be notified in advance, and continued use will constitute acceptance of the updated pricing.
              </p>
              <p className="text-lg leading-relaxed">
                You are responsible for any phone or data charges imposed by your carrier.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">User Conduct</h2>
              <p className="text-lg leading-relaxed mb-4">You agree to use the Service respectfully. The following behaviors are prohibited:</p>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>Harassment, abuse, or inappropriate language toward Emily or other users.</li>
                <li>Attempts to game, reverse-engineer, or manipulate the AI system.</li>
                <li>Sharing unauthorized content or impersonating others.</li>
              </ul>
              <p className="text-lg leading-relaxed">
                Violation may result in suspension or permanent removal from the platform.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">No Legal or Binding Agreements via AI</h2>
              <p className="text-lg leading-relaxed mb-4">
                Any communication with Emily (including AI responses, phone conversations, or text interactions) does not constitute:
              </p>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>A contract, agreement, or legal obligation.</li>
                <li>A nondisclosure agreement (NDA) or exclusivity arrangement.</li>
              </ul>
              <p className="text-lg leading-relaxed">
                Only written agreements signed by authorized representatives of Emily are enforceable.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Limitation of Liability</h2>
              <p className="text-lg leading-relaxed mb-4">
                Emily provides its services &quot;as is&quot; and does not guarantee outcomes from matches or conversations. To the maximum extent permitted by law, Emily is not liable for:
              </p>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed ml-4">
                <li>Any indirect or consequential damages.</li>
                <li>Emotional, financial, or personal results of introductions.</li>
                <li>Delays or failures in communication due to carrier issues or technical errors.</li>
              </ul>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Governing Law</h2>
              <p className="text-lg leading-relaxed">
                These Terms are governed by the laws of the State of California, United States. Any disputes shall be handled in the appropriate courts located within California.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Updates to Terms</h2>
              <p className="text-lg leading-relaxed">
                We may update these Terms from time to time. Material changes will be communicated via email or SMS. Continued use of the Service after such updates constitutes acceptance of the revised Terms.
              </p>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-black mb-4">Contact Information</h2>
              <p className="text-lg leading-relaxed">
                For questions or concerns regarding these Terms, please contact us at <a href="mailto:<EMAIL>" className="text-black hover:underline"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <Footer 
        waveBackgroundColor="#ffffff"/>
    </div>
  )
}

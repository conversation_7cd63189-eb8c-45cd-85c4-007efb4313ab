'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'

export default function AuthenticatedHome() {
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        // Not authenticated, stay on landing page
        return
      } else {
        // Check if LinkedIn is connected
        const linkedinIdentity = user.identities?.find(
          identity => identity.provider === 'linkedin_oidc'
        )
        
        if (linkedinIdentity) {
          // Both phone and LinkedIn verified, go to conversation page
          router.push('/conversation')
        } else {
          // Phone verified but no LinkedIn, go to connect page
          router.push('/auth/connect')
        }
      }
    }
    
    checkAuth()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Loading...</h1>
        <p className="text-gray-600">Checking authentication status</p>
      </div>
    </div>
  )
}

'use client';

import { useConversation } from '@elevenlabs/react';
import { useCallback, useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface ElevenLabsConversationProps {
  agentId: string;
  userId?: string;
  userName?: string;
  onBack: () => void;
  onSignOut: () => void;
  loading: boolean;
  onConversationId?: (conversationId: string) => void;
  onConversationEnd?: () => void;
}

export function ElevenLabsConversation({ 
  agentId, 
  userId,
  userName,
  onBack, 
  onSignOut, 
  loading,
  onConversationId,
  onConversationEnd
}: ElevenLabsConversationProps) {
  const [error, setError] = useState<string>('');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showIncompleteProfile, setShowIncompleteProfile] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const conversation = useConversation({
    micMuted: isMuted, // Pass the mute state to ElevenLabs
    onConnect: () => {
      console.log('Connected to Emily AI');
      setError('');
      
      // Get conversation ID and pass it to parent
      const convId = conversation.getId();
      if (convId && onConversationId) {
        console.log('Conversation ID:', convId);
        onConversationId(convId);
      }
    },
    onDisconnect: () => {
      console.log('Disconnected from Emily AI');
      // Call the parent callback when conversation ends
      if (onConversationEnd) {
        onConversationEnd();
      }
    },
    onMessage: (message) => {
      console.log('Message:', message);
    },
    onError: (error) => {
      console.error('Conversation Error:', error);
      setError('Connection error. Please try again.');
    },
    // Add overrides for personalization
    overrides: userName ? {
      agent: {
        firstMessage: `
        Hi ${userName}! I'm Emily, and I'm here to help you create an amazing dating profile through our conversation. This usually takes about 8-12 minutes, and we'll cover everything from your interests to what you're looking for in a relationship. Ready to get started?
        `,
      },
    } : undefined,
  });

  const getSignedUrl = async (): Promise<string> => {
    const response = await fetch("/api/get-signed-url");
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get signed url: ${response.statusText} - ${errorData.error}`);
    }
    const { signedUrl } = await response.json();
    return signedUrl;
  };

  const startConversation = useCallback(async () => {
    try {
      setError('');
      
      // Check if agentId is provided
      if (!agentId) {
        setError('Agent ID is not configured. Please check your environment variables.');
        return;
      }

      console.log('Starting conversation with agent ID:', agentId);
      
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Get signed URL for authentication
      console.log('Getting signed URL...');
      const signedUrl = await getSignedUrl();
      console.log('Got signed URL, starting session...');

      // Start the conversation with signed URL and user ID
      const sessionConfig: { signedUrl: string; userId?: string } = {
        signedUrl,
      };
      if (userId) {
        sessionConfig.userId = userId;
      }

      console.log('Starting session with config:', sessionConfig);
      await conversation.startSession(sessionConfig);

    } catch (error) {
      console.error('Failed to start conversation:', error);
      
      // Handle different error types
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        
        if (error.name === 'NotAllowedError') {
          setError('Microphone permission denied. Please allow microphone access and try again.');
        } else if (error.message?.includes('401') || error.message?.includes('unauthorized')) {
          setError('Authentication failed. Please check your API key configuration.');
        } else if (error.message?.includes('404') || error.message?.includes('not found')) {
          setError('Agent not found. Please verify your Agent ID is correct.');
        } else if (error.message?.includes('Failed to get signed url')) {
          setError('Authentication error. Please check your API key and Agent ID.');
        } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
          setError('Network error. Please check your internet connection.');
        } else {
          setError(`Connection failed: ${error.message}`);
        }
      } else if (error && typeof error === 'object') {
        // Handle WebSocket CloseEvent or other objects
        console.error('Non-Error object caught:', error);
        
        if ('reason' in error && error.reason) {
          setError(`Connection failed: ${error.reason}`);
        } else if ('code' in error) {
          setError(`Connection failed with code: ${error.code}`);
        } else {
          setError('Connection failed with an unknown error.');
        }
      } else {
        console.error('Unknown error type:', typeof error, error);
        setError('An unexpected error occurred. Please try again.');
      }
    }
  }, [agentId, userId, conversation]);

  const stopConversation = useCallback(async () => {
    await conversation.endSession();
    // Show incomplete profile message after ending conversation
    setShowIncompleteProfile(true);
    // The onDisconnect callback will handle calling onConversationEnd
  }, [conversation]);

  const toggleMute = useCallback(() => {
    setIsMuted(prev => {
      const newMutedState = !prev;
      console.log('Microphone muted:', newMutedState);
      return newMutedState;
    });
  }, []);

  const getStatusColor = () => {
    switch (conversation.status) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'disconnected': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (conversation.status) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      case 'disconnected': return 'Disconnected';
      default: return 'Ready';
    }
  };

  // Handle click outside to close menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex flex-col relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Header with Back Button */}
      <div className="relative flex justify-start items-center p-4 pb-0">
        <Button
          variant="ghost"
          onClick={onBack}
          className="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-white/50 rounded-xl transition-colors cursor-pointer"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back
        </Button>
      </div>

      {/* Main Content Container */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="relative w-full max-w-2xl">
        {/* Main Card */}
        <div className="p-8 mb-6">

          {/* Emily Avatar and Message - Show when connected */}
          {conversation.status === 'connected' && (
            <div className="flex justify-center mb-6">
              <div className="flex items-center">
                {/* Emily Avatar */}
                <div className="w-12 h-12 rounded-full overflow-hidden mr-3 flex-shrink-0">
                  <img 
                    src="/emily.svg" 
                    alt="Emily AI" 
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Message Bubble */}
                <div 
                  className="px-4 py-2 text-white font-medium text-sm max-w-xs"
                  style={{
                    borderRadius: '10px 10px 10px 0',
                    background: 'linear-gradient(90deg, #AC00E0 0%, #0041B0 100%)'
                  }}
                >
                  I&apos;d love to hear your story!
                </div>
              </div>
            </div>
          )}

          {/* Title Section */}
          <div className="text-center mb-8">
            <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-[#005EFF] to-[#99008A] bg-clip-text text-transparent">
              Let&apos;s find your love
            </h1>
            <p className="text-lg" style={{ color: '#005EFF' }}>
              Click the widget to start your call with Emily.
            </p>
          </div>

          {/* Connected Status Box */}
          {conversation.status === 'connected' && (
            <div className="bg-white rounded-2xl border border-gray-200/60 p-6 mb-6 transition-all duration-300 hover:border-gray-300/60 max-w-md mx-auto">
              {/* Emily Avatar and Status */}
              <div className="flex items-center justify-center mb-6">
                {/* Emily Avatar */}
                <div className="relative mr-4">
                  <div className="w-14 h-14 rounded-full overflow-hidden ring-2 ring-gray-100">
                    <img 
                      src="/emily.svg" 
                      alt="Emily AI" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {/* Speaking Indicator */}
                  {conversation.isSpeaking && (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    </div>
                  )}
                </div>
                
                {/* Status Text */}
                <div className="flex flex-col items-start">
                  <div className={`text-lg font-medium transition-colors duration-200 ${
                    conversation.isSpeaking ? 'text-blue-600' : 'text-gray-700'
                  }`}>
                    {conversation.isSpeaking ? 'Emily is talking...' : 'You are talking...'}
                  </div>
                  {/* Subtle typing indicator */}
                  <div className="flex space-x-1 mt-1">
                    {conversation.isSpeaking && (
                      <>
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-center gap-3">
                {/* End Call Button */}
                <Button
                  onClick={stopConversation}
                  className="px-8 py-3 bg-[#005EFF] hover:bg-[#0052E0] text-white font-medium text-base rounded-xl transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md"
                >
                  <div className="flex items-center justify-center">
                    <svg className="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    End Call
                  </div>
                </Button>
                
                {/* Microphone Button */}
                <button
                  onClick={toggleMute}
                  className={`w-11 h-11 rounded-xl flex items-center justify-center transition-all duration-200 cursor-pointer ${
                    isMuted 
                      ? 'bg-red-50 hover:bg-red-100 border border-red-200' 
                      : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
                  }`}
                >
                  <svg 
                    className={`w-4 h-4 ${isMuted ? 'text-red-500' : 'text-gray-600'}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    {isMuted ? (
                      <>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3l18 18" />
                      </>
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    )}
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Profile Incomplete Message */}
          {showIncompleteProfile && conversation.status === 'disconnected' && (
            <div className="bg-blue-50 border border-blue-200 rounded-2xl p-6 mb-6">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-blue-800 mb-2">
                  Your Profile Isn&apos;t Complete Yet
                </h3>
                <p className="text-blue-700 mb-4">
                  It looks like we need a bit more time to get to know you better. Feel free to chat with us again anytime to continue building your profile!
                </p>
                <Button
                  onClick={() => setShowIncompleteProfile(false)}
                  className="px-6 py-2 bg-gradient-to-r from-[#005EFF] to-[#4285F4] hover:from-[#0052E0] hover:to-[#3367D6] text-white font-medium rounded-xl transition-all duration-200 cursor-pointer"
                >
                  Start New Call
                </Button>
              </div>
            </div>
          )}

          {/* Control Buttons - Only show when not connected and not showing incomplete profile */}
          {conversation.status !== 'connected' && !showIncompleteProfile && (
            <div className="flex gap-6 justify-center mb-6">
              <Button
                onClick={startConversation}
                disabled={conversation.status === 'connecting'}
                className="px-8 py-4 bg-gradient-to-r from-[#005EFF] to-[#4285F4] hover:from-[#0052E0] hover:to-[#3367D6] text-white font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer min-w-[180px]"
                style={{ borderRadius: '10px' }}
              >
                {conversation.status === 'connecting' ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    Connecting...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                    Start a Web Call
                  </div>
                )}
              </Button>
              
              <Button
                onClick={() => {
                  // TODO: Implement phone call functionality
                  console.log('Phone call functionality to be implemented')
                }}
                className="px-8 py-4 bg-gradient-to-r from-[#005EFF] to-[#4285F4] hover:from-[#0052E0] hover:to-[#3367D6] text-white font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer min-w-[180px]"
                style={{ borderRadius: '10px' }}
              >
                <div className="flex items-center justify-center">
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  Start a Phone Call
                </div>
              </Button>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-red-800 font-medium">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        {conversation.status === 'disconnected' && !error && (
          <div className="bg-blue-50 border border-blue-100 rounded-2xl p-6 mb-6">
            <h3 className="text-blue-800 font-semibold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              Before you start:
            </h3>
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-3 mt-2"></div>
                <span className="text-blue-800 text-sm">Make sure you&apos;re in a quiet environment</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-3 mt-2"></div>
                <span className="text-blue-800 text-sm">Allow microphone access when prompted</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-3 mt-2"></div>
                <span className="text-blue-800 text-sm">Speak clearly and naturally</span>
              </div>
              {/* <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mr-3 mt-2"></div>
                <span className="text-blue-800 text-sm">The conversation will be recorded for analysis</span>
              </div> */}
            </div>
          </div>
        )}
        </div>
      </div>

      {/* User Menu - Bottom Left */}
      <div className="absolute bottom-4 left-4 z-50" ref={menuRef}>
        {/* User Menu Button */}
        <button
          onClick={() => setShowUserMenu(!showUserMenu)}
          className="flex items-center space-x-3 px-3 py-2 rounded-lg bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:bg-white/95 transition-all duration-200 cursor-pointer shadow-sm"
        >
          {/* User Avatar */}
          <div className="w-7 h-7 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-md flex items-center justify-center text-white font-medium text-xs flex-shrink-0">
            {userName ? userName.charAt(0).toUpperCase() : 'U'}
          </div>
          
          {/* User Info - Show on small screens and up */}
          <div className="text-left hidden sm:block">
            <div className="text-xs font-medium text-gray-900 whitespace-nowrap">
              {userName || 'User'}
            </div>
            <div className="text-xs text-blue-600 whitespace-nowrap">Beta version</div>
          </div>
          
          {/* Chevron Icon */}
          <svg 
            className={`w-3 h-3 text-gray-500 transition-transform duration-200 flex-shrink-0 ${showUserMenu ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </button>

        {/* Dropdown Menu */}
        {showUserMenu && (
          <div className="absolute bottom-full left-0 mb-2 bg-white/95 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg overflow-hidden"
               style={{ width: 'max-content', minWidth: '100%' }}>
            <div className="py-1">
              <button
                onClick={() => {
                  setShowUserMenu(false);
                  onSignOut();
                }}
                disabled={loading}
                className="w-full px-4 py-3 text-left flex items-center space-x-3 hover:bg-gray-50/80 transition-colors disabled:opacity-50 cursor-pointer text-sm whitespace-nowrap"
              >
                <svg className="w-4 h-4 text-gray-500" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 22H5C3.34 22 2 20.66 2 19V5C2 3.34 3.34 2 5 2H19C20.66 2 22 3.34 22 5V19C22 20.66 20.66 22 19 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 6V18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M6 12L12 6L18 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="font-medium text-gray-700">
                  {loading ? 'Signing out...' : 'Log out'}
                </span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
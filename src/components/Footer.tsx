'use client'

import React from 'react'
import { useRouter } from 'next/navigation'

interface FooterProps {
  backgroundColor?: string;
  waveBackgroundColor?: string;
  waveFillColor?: string;
}

export default function Footer({ 
  backgroundColor = '#1e3a8a', 
  waveBackgroundColor = '#EFFFFE',
  waveFillColor = '#1e3a8a'
}: FooterProps) {
  const router = useRouter()

  return (
    <footer className="py-6 relative" style={{ backgroundColor }}>
      {/* Wave SVG */}
      <div className="absolute top-0 left-0 w-full overflow-hidden" style={{ backgroundColor: waveBackgroundColor, transform: 'translateY(-1px)' }}>
        <svg 
          viewBox="0 0 1440 120" 
          preserveAspectRatio="none" 
          className="relative block w-full h-[40px]"
        >
          <path 
            d="M0.445068 13.4961C179.445 32.0958 443.734 27.9922 443.734 27.9922C443.765 27.9919 617.621 26.5384 836.258 8.17969C1041.25 -9.03314 1392.91 6.02868 1436.06 7.97852L1440.45 8.17969V120L0.445068 120V13.4961Z" 
            fill={waveFillColor}
          />
        </svg>
      </div>
      <div className="max-w-7xl mx-auto px-6 pt-10">
        {/* Desktop: Single row layout */}
        <div className="hidden md:flex justify-between items-center">
          {/* Left side */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <img 
                src="/emily.svg" 
                alt="Emily AI Logo" 
                className="w-11 h-11"
              />
              <span className="text-xl font-bold text-white">Emily</span>
            </div>
            
            {/* Beta version on desktop */}
            <div className="inline-flex items-center justify-center space-x-1 border border-blue-400 rounded-full px-3 py-1 bg-blue-800">
              <span className="text-blue-200 font-semibold text-xs">★</span>
              <span className="text-blue-200 text-xs font-medium text-center whitespace-nowrap">Beta version - we&apos;d love your thoughts!</span>
            </div>
          </div>
          
          {/* Right side */}
          <div className="flex items-center space-x-6 text-sm text-white">
            <button
              onClick={() => router.push('/privacy')}
              className="hover:text-white transition-colors whitespace-nowrap"
            >
              Privacy Policy
            </button>
            <button
              onClick={() => router.push('/terms')}
              className="hover:text-white transition-colors whitespace-nowrap"
            >
              Terms of Service
            </button>
            <span className="whitespace-nowrap">© 2025 Powered and secured by heyemily.ai</span>
          </div>
        </div>
        
        {/* Mobile: Double row layout */}
        <div className="md:hidden space-y-2">
          {/* First row: Logo and links */}
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <img 
                src="/emily.svg" 
                alt="Emily AI Logo" 
                className="w-11 h-11"
              />
              <span className="text-xl font-bold text-white">Emily</span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-white">
              <button
                onClick={() => router.push('/privacy')}
                className="hover:text-white transition-colors whitespace-nowrap"
              >
                Privacy Policy
              </button>
              <button
                onClick={() => router.push('/terms')}
                className="hover:text-white transition-colors whitespace-nowrap"
              >
                Terms of Service
              </button>
            </div>
          </div>
          
          {/* Second row: Copyright */}
          <div className="text-right">
            <span className="text-sm text-white whitespace-nowrap">© 2025 Powered and secured by heyemily.ai</span>
          </div>
        </div>
      </div>
    </footer>
  )
}

'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { supabase } from '@/lib/supabase'
import Footer from './Footer'
import { AuthLoading } from '@/components/ui/loading'

interface ComparisonCardProps {
  title: string;
  subtitle?: string;
  isPositive: boolean;
  items: string[];
}

const ComparisonCard = ({ title, subtitle, isPositive, items }: ComparisonCardProps) => {
  const iconSrc = isPositive ? "/icons/check.svg" : "/icons/no.svg";
  const cardBg = isPositive 
    ? "bg-white border border-[#e7e9ed]" 
    : "bg-gradient-to-br from-blue-800/90 to-blue-900/90 backdrop-blur-sm border-2 border-cyan-400/30";
  const textColor = isPositive ? "text-[#161c2d]" : "text-white";
  const titleColor = isPositive ? "text-white" : "text-cyan-400";
  
  // Specific icons for positive card items
  const positiveIcons = [
    "/icons/chat_round_heart.svg",
    "/icons/yeah.svg", 
    "/icons/phone_success.svg",
    "/icons/user_circle.svg"
  ];
  
  // Specific icons for negative card items
  const negativeIcons = [
    "/icons/heart_zoom.svg",
    "/icons/robot.svg",
    "/icons/users.svg", 
    "/icons/refresh_history.svg"
  ];

  return (
    <div className="w-full">
      {/* Header */}
      <div className="mb-4 flex items-end gap-3 flex-wrap">
        <h3 className={`text-2xl sm:text-3xl lg:text-4xl font-bold ${titleColor} leading-none`}>
          {title}
        </h3>
        {subtitle && (
          <span className="text-sm font-bold text-white tracking-wider uppercase leading-none">
            {subtitle}
          </span>
        )}
      </div>
      
      {/* Card */}
      <div className={`${cardBg} rounded-lg shadow-lg p-6 sm:p-8 relative`}>
        {/* Corner Icon */}
        <div className="absolute top-4 right-6">
          <img 
            src={iconSrc} 
            alt={isPositive ? "Check" : "No"} 
            className="w-6 h-6 sm:w-7 sm:h-7" 
          />
        </div>
        
        {/* Items List */}
        <div className="space-y-4 pt-4">
          {items.map((item, index) => (
            <div key={index} className="flex items-center gap-3">
              <img 
                src={isPositive ? (positiveIcons[index] || iconSrc) : (negativeIcons[index] || iconSrc)} 
                alt={isPositive ? "Feature" : "Issue"} 
                className="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0" 
              />
              <span className={`text-base sm:text-lg lg:text-xl font-medium ${textColor} leading-relaxed`}>
                {item}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Company data for logo marquee with precise heights
const companies = [
  { name: 'Amazon', logoPath: '/icons/companies/amazon.svg', height: 19 },
  { name: 'Binance', logoPath: '/icons/companies/binance.svg', height: 22 },
  { name: 'BlackRock', logoPath: '/icons/companies/blackrock.svg', height: 13 },
  { name: 'Intel', logoPath: '/icons/companies/intel.svg', height: 16 },
  { name: 'Google', logoPath: '/icons/companies/google.svg', height: 24 },
  { name: 'Meta', logoPath: '/icons/companies/Meta Platforms, Inc..svg', height: 16 },
  { name: 'Microsoft', logoPath: '/icons/companies/Microsoft Corporation.svg', height: 20 },
  { name: 'Morgan Stanley', logoPath: '/icons/companies/Morgan Stanley.svg', height: 23 },
  { name: 'Notion', logoPath: '/icons/companies/notion.svg', height: 19 },
  { name: 'NVIDIA', logoPath: '/icons/companies/Nvidia.svg', height: 17 },
  { name: 'OpenAI', logoPath: '/icons/companies/openai.png', height: 23 },
  { name: 'Y Combinator', logoPath: '/icons/companies/ycombiner.png', height: 18 },
]

// Function to get logo height classes
const getLogoHeight = (company: { height: number }) => {
  // Use original height with slight scaling for visual consistency
  const desktopHeight = Math.round(company.height * 0.9) // Scale down slightly for better fit
  const mobileHeight = Math.round(desktopHeight * 0.8)   // 80% for mobile
  
  return {
    desktopHeight: `${desktopHeight}px`,
    mobileHeight: `${mobileHeight}px`
  }
}

export default function LandingPage() {
  const router = useRouter()
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        // User is authenticated, redirect based on LinkedIn connection
        const linkedinIdentity = user.identities?.find(
          identity => identity.provider === 'linkedin_oidc'
        )
        
        if (linkedinIdentity) {
          // Both phone and LinkedIn verified, go to conversation page
          router.push('/conversation')
          return
        } else {
          // Phone verified but no LinkedIn, go to connect page
          router.push('/auth/connect')
          return
        }
      }
      
      // User is not authenticated, show landing page
      setIsCheckingAuth(false)
    }
    
    checkAuth()
  }, [router])

  if (isCheckingAuth) {
    return <AuthLoading />
  }

  const handleGetStarted = () => {
    router.push('/auth/login')
  }

  const handleCallNow = () => {
    // For now, redirect to login. Later this could open a phone call interface
    router.push('/auth/login')
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      const headerHeight = 100 // Account for fixed header
      const elementPosition = element.offsetTop - headerHeight
      
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      })
    }
  }

  const handleInvitationCode = () => {
    scrollToSection('ready-to-start')
  }

  return (
    <div className="min-h-screen bg-white min-w-[320px] lg:min-w-[1183px]">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between xl:min-w-[950px]">
          {/* Logo */}
          <button 
            onClick={() => router.push('/')}
            className="flex items-center space-x-3 hover:opacity-80 transition-opacity cursor-pointer"
          >
            <img 
              src="/emily.svg" 
              alt="Emily AI Logo" 
              className="w-8 h-8 md:w-11 md:h-11"
            />
            <span className="text-lg md:text-xl font-bold text-[#005EFF]">Emily</span>
          </button>

          {/* Desktop Navigation */}
          <nav className="hidden xl:flex w-[775px] min-w-[775px] h-[65px] items-center justify-between px-10 py-2 rounded-lg backdrop-blur-md" style={{ backgroundColor: '#0077FF33' }}>
            <button onClick={handleInvitationCode} className="text-[#5594FF] font-semibold hover:bg-[#99CFFF] flex items-center justify-center h-[53px] px-[11px] py-[13px] rounded-[10px] transition-colors text-[18px] cursor-pointer">
              Invitation Code
            </button>
            <button onClick={() => scrollToSection('standout')} className="text-[#5594FF] font-semibold hover:bg-[#99CFFF] flex items-center justify-center h-[53px] px-[11px] py-[13px] rounded-[10px] transition-colors text-[18px] cursor-pointer">
              How I Stand out
            </button>
            <button onClick={() => scrollToSection('howwork')} className="text-[#5594FF] font-semibold hover:bg-[#99CFFF] flex items-center justify-center h-[53px] px-[11px] py-[13px] rounded-[10px] transition-colors text-[18px] cursor-pointer">
              How I work
            </button>
            <button
              onClick={handleCallNow}
              className="bg-[#005eff] flex w-[185px] h-[53px] gap-[8px] items-center justify-center px-[11px] py-[13px] rounded-[10px] hover:bg-[#0099FF] transition-colors cursor-pointer"
            >
              <img src="/icons/phone.svg" alt="Phone" className="w-[18px] h-[18px]" />
              <span className="capitalize font-bold leading-[1.5] text-[18px] text-center text-nowrap text-white whitespace-pre">
                Call me now
              </span>
            </button>
          </nav>

          {/* Sign In Button - responsive sizing */}
          <button
            onClick={() => router.push('/auth/login')}
            className="flex w-[80px] h-[40px] md:w-[121px] md:h-[51px] p-[8px] md:p-[11px] justify-center items-center gap-[10px] flex-shrink-0 rounded-[8px] md:rounded-[10px] font-semibold text-[#005eff] bg-[#0077FF33] hover:bg-[#99cfff] transition-colors cursor-pointer text-sm md:text-base"
          >
            Sign in
          </button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative px-6 py-16" style={{ backgroundColor: '#EFFCFF' }}>
        <div className="max-w-7xl mx-auto relative">
          <div className="space-y-6 lg:w-1/2 lg:pr-8 text-center lg:text-left">
            {/* Beta Badge */}
            <div className="inline-flex items-center justify-center space-x-2 bg-blue-100 px-4 py-2 mt-[60px] lg:mt-[113px]" style={{ borderRadius: '24px' }}>
              <span className="text-blue-600 font-semibold">★</span>
              <span className="text-blue-600 text-sm font-medium text-center">Beta version - we&apos;d love your thoughts!</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              <span className="text-blue-900">Let me find your</span>
              <br />
              <span className="text-blue-500">soulmate</span> <span className="text-blue-900">who can</span>
              <br />
              <span className="text-blue-900">change your life.</span>
            </h1>

            {/* Subtitle */}
            <p className="text-base md:text-lg text-blue-700 max-w-lg mx-auto lg:mx-0">
              I&apos;m the the most connected AI Super Matchmaker who introduces you to your soulmate.
            </p>

            {/* CTA Button */}
            <div className="flex justify-center lg:justify-start">
              <button
                onClick={handleGetStarted}
                className="bg-[#005EFF] w-[269px] text-white px-[24px] py-[16px] rounded-lg text-[24px] font-[700] line-height-[28px] transition-colors hover:bg-[#0099FF] cursor-pointer"
              >
                Find your soulmate
              </button>
            </div>

            {/* Privacy Note */}
            <p className="text-sm text-blue-600">
              <span className="font-light">By signing up, you confirm you have read and accept our </span>
              <button
                onClick={() => router.push('/privacy')}
                className="font-medium hover:underline"
              >
                Privacy Policy.
              </button>
            </p>
          </div>

          {/* Phone Mockup */}
          <div className="relative z-10 lg:absolute lg:right-0 lg:w-1/2 flex items-start justify-center" style={{ top: '30px' }}>
            <img 
              src="/icons/mock_iphone.png" 
              alt="Emily AI iPhone Mockup" 
              className="w-full max-w-sm lg:max-w-none lg:w-4/5 h-auto drop-shadow-2xl transform translate-y-[60px] lg:translate-y-20"
            />
          </div>
        </div>
      </section>

      {/* Company Logos */}
      <section className="bg-blue-600 py-8 relative z-20 -mt-[240px] md:-mt-[240px] lg:mt-0">
        <div className="max-w-7xl mx-auto px-6">
          <p className="text-center text-blue-100 text-sm font-semibold mb-6 opacity-80">
            I Match People From Top Companies Like…
          </p>
          <div className="relative overflow-hidden">
            {/* Gradient masks for smooth fade effect */}
            <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-blue-600 to-transparent z-10"></div>
            <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-blue-600 to-transparent z-10"></div>
            
            {/* Marquee container */}
            <div className="marquee-container">
              <div className="marquee-content">
                {/* First set of logos */}
                {companies.map((company, index) => (
                  <div key={`first-${index}`} className="flex-shrink-0 mx-4 sm:mx-6 flex items-center justify-center min-w-[60px] sm:min-w-[80px]">
                    <img
                      src={company.logoPath}
                      alt={`${company.name} logo`}
                      style={{
                        '--desktop-height': getLogoHeight(company).desktopHeight,
                        '--mobile-height': getLogoHeight(company).mobileHeight,
                      } as React.CSSProperties}
                      className="logo-responsive opacity-80 hover:opacity-100 transition-opacity duration-300 object-contain"
                      onError={(e) => {
                        // Fallback to text if logo fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'block';
                      }}
                    />
                    <div className="hidden text-white font-semibold text-sm opacity-70">
                      {company.name}
                    </div>
                  </div>
                ))}
                {/* Duplicate set for seamless loop */}
                {companies.map((company, index) => (
                  <div key={`second-${index}`} className="flex-shrink-0 mx-4 sm:mx-6 flex items-center justify-center min-w-[60px] sm:min-w-[80px]">
                    <img
                      src={company.logoPath}
                      alt={`${company.name} logo`}
                      style={{
                        '--desktop-height': getLogoHeight(company).desktopHeight,
                        '--mobile-height': getLogoHeight(company).mobileHeight,
                      } as React.CSSProperties}
                      className="logo-responsive opacity-80 hover:opacity-100 transition-opacity duration-300 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'block';
                      }}
                    />
                    <div className="hidden text-white font-semibold text-sm opacity-70">
                      {company.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How I Stand Out */}
      <section 
        id="standout" 
        className="py-16 md:py-20 lg:py-24 relative z-20" 
        style={{ background: 'linear-gradient(180deg, #0008a1, #00056c)' }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-center text-white mb-12 md:mb-16 lg:mb-20">
            How I Stand Out
          </h2>
          
          {/* Mobile Layout */}
          <div className="block lg:hidden space-y-8">
            {/* With Emily Card */}
            <div className="w-full max-w-md mx-auto">
              <ComparisonCard
                title="With Emily"
                subtitle="/ BETA"
                isPositive={true}
                items={[
                  "Like a friend who introduces warmly",
                  "No swiping, ghosting, wasted time", 
                  "Talk by call or text anytime",
                  "Real and authentic connections"
                ]}
              />
            </div>
            
            {/* VS Divider */}
            <div className="flex justify-center py-4">
              <img src="/icons/vs.svg" alt="VS" className="w-20 h-14" />
            </div>
            
            {/* Most Dating Apps Card */}
            <div className="w-full max-w-md mx-auto">
              <ComparisonCard
                title="Most Dating Apps"
                isPositive={false}
                items={[
                  "Endless swiping",
                  "90% ghosting rates, energy burnout",
                  "Built to keep your time spent", 
                  "Quantity over quality"
                ]}
              />
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex items-center justify-center gap-8">
            {/* With Emily Card */}
            <div className="flex-1 max-w-lg">
              <ComparisonCard
                title="With Emily"
                subtitle="/ BETA"
                isPositive={true}
                items={[
                  "Like a friend who introduces warmly",
                  "No swiping, ghosting, wasted time",
                  "Talk by call or text anytime", 
                  "Real and authentic connections"
                ]}
              />
            </div>
            
            {/* VS Divider - positioned to align with the middle of the cards */}
            <div className="flex items-center justify-center pt-16">
              <img src="/icons/vs.svg" alt="VS" className="w-24 h-16" />
            </div>
            
            {/* Most Dating Apps Card */}
            <div className="flex-1 max-w-lg">
              <ComparisonCard
                title="Most Dating Apps"
                isPositive={false}
                items={[
                  "Endless swiping",
                  "90% ghosting rates, energy burnout", 
                  "Built to keep your time spent",
                  "Quantity over quality"
                ]}
              />
            </div>
          </div>
          
          {/* Begin with Emily Button */}
          <div className="flex justify-center mt-12 lg:mt-16">
            <button className="bg-[#005eff] hover:bg-[#0099FF] transition-colors duration-200 flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-bold text-lg text-white whitespace-nowrap cursor-pointer"
            onClick={handleGetStarted}>
              <img src="/icons/phone.svg" alt="Phone" className="w-5 h-5" />
              Begin with Emily
            </button>
          </div>
        </div>
      </section>

      {/* How I Work */}
      <section id="howwork" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-5xl font-bold text-center text-blue-600 mb-16">How I Work</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="mb-6 mx-auto max-w-sm">
                <Image
                  src="/icons/how_1.svg"
                  alt="Step 1: Sign up"
                  width={387}
                  height={290}
                  className="w-full h-auto"
                  priority
                  quality={100}
                  unoptimized
                />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 mb-3">1. Sign up</h3>
              <p className="text-gray-600">
                Sign up is quick and simple, and it helps me prepare to learn your story.
              </p>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="mb-6 mx-auto max-w-sm">
                <Image
                  src="/icons/how_2.svg"
                  alt="Step 2: I'll give you a call"
                  width={388}
                  height={290}
                  className="w-full h-auto"
                  priority
                  quality={100}
                  unoptimized
                />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 mb-3">2. I&apos;ll give you a call</h3>
              <p className="text-gray-600">
                I&apos;ll give you a call, share your story, who you are, and the relationship you&apos;re looking for.
              </p>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="mb-6 mx-auto max-w-sm">
                <Image
                  src="/icons/how_3.svg"
                  alt="Step 3: I'll make matches"
                  width={388}
                  height={290}
                  className="w-full h-auto"
                  priority
                  quality={100}
                  unoptimized
                />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 mb-3">3. I&apos;ll make matches</h3>
              <p className="text-gray-600">
                When I find someone compatible, I&apos;ll introduce you with context for a natural first conversation.
              </p>
            </div>
          </div>

          <div className="flex justify-center mt-12">
            <button
              onClick={handleGetStarted}
              className="bg-[#005eff] hover:bg-[#0099FF] transition-colors duration-200 flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-bold text-lg text-white whitespace-nowrap cursor-pointer"
            >
              <img src="/icons/phone.svg" alt="Phone" className="w-5 h-5" />
              Speak to Emily
            </button>
          </div>
        </div>
      </section>

      {/* Ready to get started */}
      <section id="ready-to-start" className="bg-blue-600 py-12">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <div className="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-8">
            <h2 className="text-3xl font-bold text-white">Ready to get started?</h2>
            <button
              onClick={() => {
                const invitationFormUrl = process.env.NEXT_PUBLIC_INVITATION_FORM
                if (invitationFormUrl) {
                  window.open(invitationFormUrl, '_blank')
                } else {
                  handleGetStarted()
                }
              }}
              className="bg-white px-8 py-3 rounded-lg text-lg font-bold hover:bg-[#fadeff] transition-colors cursor-pointer"
            >
              <span className="bg-gradient-to-r from-[#ff0075] to-[#065bfb] bg-clip-text text-transparent">
                Get your Invitation Code
              </span>
            </button>
          </div>
        </div>
      </section>

      {/* Call Emily Now */}
      <section className="py-16 bg-[#EFFFFE] relative z-10">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-5xl font-bold bg-gradient-to-r from-[#005eff] to-[#99008a] bg-clip-text text-transparent mb-8">
            Call Emily now.
          </h2>
          <div className="flex justify-center mb-6">
            <button
              onClick={handleCallNow}
              className="bg-[#005eff] hover:bg-[#0099FF] transition-colors duration-200 flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-bold text-lg text-white whitespace-nowrap cursor-pointer"
            >
              <img src="/icons/phone.svg" alt="Phone" className="w-5 h-5" />
              Serious Connections Only
            </button>
          </div>
          
          {/* Beta Version - Only shown on mobile */}
          <div className="flex justify-center md:hidden">
            <div className="inline-flex items-center justify-center space-x-2 border border-blue-400 rounded-full px-4 py-2 bg-white">
              <span className="text-blue-600 font-semibold">★</span>
              <span className="text-blue-600 text-sm font-medium text-center">Beta version - we&apos;d love your thoughts!</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  )
}

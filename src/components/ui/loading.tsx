import React from 'react'
import { Spinner } from './spinner'
import { cn } from '@/lib/utils'

interface LoadingProps {
  title?: string
  description?: string
  variant?: 'default' | 'circle' | 'pinwheel' | 'circle-filled' | 'ellipsis' | 'ring'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  fullScreen?: boolean
  showLogo?: boolean
}

const sizeMap = {
  sm: 16,
  md: 24,
  lg: 32
}

export function Loading({
  title = 'Loading...',
  description,
  variant = 'circle',
  size = 'md',
  className,
  fullScreen = true,
  showLogo = false
}: LoadingProps) {
  const content = (
    <div className={cn(
      'flex flex-col items-center justify-center text-center',
      fullScreen ? 'min-h-screen' : 'py-8',
      className
    )}>
      {showLogo && (
        <div className="mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl shadow-lg">
            <img 
              src="/emily.svg" 
              alt="Emily AI" 
              className="w-8 h-8 text-white"
            />
          </div>
        </div>
      )}
      
      <div className="mb-4">
        <Spinner 
          variant={variant} 
          size={sizeMap[size]} 
          className="text-blue-600"
        />
      </div>
      
      <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
        {title}
      </h2>
      
      {description && (
        <p className="text-gray-600 max-w-md">
          {description}
        </p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        {content}
      </div>
    )
  }

  return content
}

// 预设的加载组件变体
export const AuthLoading = () => (
  <Loading
    title="Loading..."
    description="Checking authentication status"
    showLogo={true}
    variant="circle"
  />
)

export const ProfileLoading = () => (
  <Loading
    title="Loading..."
    description="Checking your profile status"
    showLogo={true}
    variant="circle"
  />
)

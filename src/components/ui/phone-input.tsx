"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

export interface PhoneInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value?: string
  onChange?: (value: string) => void
}

const PhoneInput = React.forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ className, value = "", onChange, ...props }, ref) => {
    const [displayValue, setDisplayValue] = React.useState("+1 ")

    // Format phone number - +1 is always fixed at the start
    const formatPhoneNumber = (digits: string): string => {
      if (digits.length === 0) return "+1 "
      
      let formatted = "+1 "
      
      if (digits.length <= 3) {
        formatted += `(${digits}`
      } else if (digits.length <= 6) {
        formatted += `(${digits.slice(0, 3)}) ${digits.slice(3)}`
      } else {
        formatted += `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`
      }
      
      return formatted
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let input = e.target.value
      
      // 确保 +1 始终在开头，如果用户删除了就加回来
      if (!input.startsWith("+1 ")) {
        // 如果用户试图删除 +1，从输入中提取数字部分
        const digitsOnly = input.replace(/\D/g, '')
        input = "+1 " + input.slice(input.search(/\d/))
      }
      
      // 从 +1 后面提取数字
      const afterPrefix = input.slice(3) // 跳过 "+1 "
      const digits = afterPrefix.replace(/\D/g, '').slice(0, 10) // 最多10位数字
      
      // 格式化显示
      const formatted = formatPhoneNumber(digits)
      setDisplayValue(formatted)
      
      // 返回完整的格式化号码
      onChange?.(formatted)
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      const input = e.currentTarget
      const cursorPosition = input.selectionStart || 0
      
      // 防止删除 "+1 " 前缀
      if ((e.key === 'Backspace' || e.key === 'Delete') && cursorPosition <= 3) {
        e.preventDefault()
      }
    }

    // 处理外部值变化
    React.useEffect(() => {
      if (value && value !== displayValue) {
        const digits = value.replace(/\D/g, '')
        // 如果有国家代码1，去掉它
        const phoneDigits = digits.startsWith('1') ? digits.slice(1) : digits
        setDisplayValue(formatPhoneNumber(phoneDigits.slice(0, 10)))
      }
    }, [value])

    return (
      <div className="relative">
        <Input
          type="tel"
          placeholder="+****************"
          className={cn(
            "pl-4 text-lg font-medium",
            className
          )}
          value={displayValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          ref={ref}
          {...props}
        />
      </div>
    )
  }
)

PhoneInput.displayName = "PhoneInput"

export { PhoneInput }
